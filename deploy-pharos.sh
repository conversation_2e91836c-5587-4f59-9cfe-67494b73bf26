#!/bin/bash

# 设置脚本在遇到错误时退出
set -e

# 构建 Docker 镜像
docker build --no-cache --pull --rm -f Dockerfile.pharos -t moofun-pharos .

# 停止并删除旧的容器（如果存在）
docker stop moofun-pharos-container 2>/dev/null || true
docker rm moofun-pharos-container 2>/dev/null || true

docker image prune -f

# 运行新的容器实例
docker run -d -p 9113:3457 --name moofun-pharos-container --network wolf_fun moofun-pharos

docker exec moofun-pharos-container npm run seed:tasks