// /src/jobs/bullmqConfig.ts
import { Queue } from "bullmq";
import IORedis from "ioredis";

import dotenv from "dotenv";
dotenv.config();

console.log('[BullMQ] 正在初始化 Redis 连接...');
const connection = new IORedis({
    host: process.env.REDIS_HOST || 'redis',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASS || '',
    maxRetriesPerRequest: null,
    enableReadyCheck: true,
    retryStrategy: (times: number) => {
        console.log(`[BullMQ] Redis 连接重试次数: ${times}`);
        return Math.min(times * 100, 3000);
    },
    reconnectOnError: (err: Error) => {
        console.error('[BullMQ] Redis 连接错误:', err);
        const targetError = 'READONLY';
        if (err.message.includes(targetError)) {
            return true;
        }
        return false;
    }
});

connection.on('connect', () => {
    console.log('[BullMQ] Redis 已连接');
});

connection.on('ready', () => {
    console.log('[BullMQ] Redis 准备就绪');
});

connection.on('error', (error) => {
    console.error('[BullMQ] Redis 错误:', error);
});

connection.on('close', () => {
    console.log('[BullMQ] Redis 连接已关闭');
});

connection.on('reconnecting', () => {
    console.log('[BullMQ] Redis 正在重新连接...');
});

console.log('[BullMQ] Redis 连接已初始化，正在创建任务队列...');
export const jobQueue = new Queue("lottery-result-job", { connection });
export const kaiaPriceUpdateQueue = new Queue("kaia-price-update-job", { connection });
export const phrsPriceUpdateQueue = new Queue("phrs-price-update-job", { connection });
console.log('[BullMQ] 任务队列已创建完成');